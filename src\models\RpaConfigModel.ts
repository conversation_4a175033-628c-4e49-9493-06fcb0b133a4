import {
  RPA_CONFIG_NUMBER_OF_RETRY_MIN_VALUE,
  RPA_CONFIG_NUMBER_OF_RETRY_MAX_VALUE,
  RPA_CONFIG_INTERVAL_SECOND_MIN_VALUE,
  RPA_CONFIG_INTERVAL_SECOND_MAX_VALUE,
} from '@common/constants/ValidationConstant';
import { z } from 'zod';

export const RpaConfigModelSchema = z.object({
  id: z.string().optional(),
  interval: z.number().min(RPA_CONFIG_INTERVAL_SECOND_MIN_VALUE).max(RPA_CONFIG_INTERVAL_SECOND_MAX_VALUE),
  numberOfRetry: z.number().min(RPA_CONFIG_NUMBER_OF_RETRY_MIN_VALUE).max(RPA_CONFIG_NUMBER_OF_RETRY_MAX_VALUE),
  active: z.boolean().optional(),
});

export type RpaConfigModel = z.infer<typeof RpaConfigModelSchema>;
