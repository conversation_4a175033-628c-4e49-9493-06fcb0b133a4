export type Service = 'server' | 'external-mail' | 'external-database' | 'external-webhook' | 'external-job' | 'external-rts';
export const getConfigs = () => {
  const apiBaseUrl = process.env.REACT_APP_MBMONITOR_API_URL || '';
  const apiBaseUrlRemote = process.env.REACT_APP_MBMONITOR_API_URL_REMOTE || apiBaseUrl;

  const apiServiceRemote = (process.env.REACT_APP_MONITOR_API_SERVICE_REMOTE || '').split(',');

  const isDevMode = process.env.NODE_ENV === 'development';

  return {
    name: process.env.REACT_APP_NAME || '',
    fullname: process.env.REACT_APP_FULLNAME || '',
    description: process.env.REACT_APP_DESCRIPTION || '',
    deployUrl: process.env.REACT_APP_DEPLOY_URL || '',
    apiBaseUrl: apiBaseUrl,
    apiBaseUrlRemote: apiBaseUrlRemote,
    buildApiBaseUrl: (service: Service = 'server') => {
      const map: Record<Service, string> = {
        server: '/api',
        'external-mail': '/api/external-mail',
        'external-database': '/api/external-database',
        'external-webhook': '/api/external-webhook',
        'external-job': '/api/external-job',
        'external-rts': '/api/rts',
      };
      const baseUrl = map[service];
      if (!isDevMode || !apiServiceRemote.includes(service)) {
        return apiBaseUrl + baseUrl;
      }
      return apiBaseUrlRemote + baseUrl;
    },
    apiWebHookCollect: process.env.REACT_APP_MBMONITOR_API_WEBHOOK_COLLECT || '',
    keycloak: {
      enable: process.env.REACT_APP_KEYCLOAK_ENABLED === 'true',
      url: process.env.REACT_APP_KEYCLOAK_URL || '',
      realm: process.env.REACT_APP_KEYCLOAK_REALM || '',
      clientId: process.env.REACT_APP_KEYCLOAK_CLIENT_ID || '',
    },
    maxFileAttachSize: Number(process.env.MAX_FILE_ATTACH_SIZE) || 8 * 1024 * 1024,
    maxEmailAttachSize: Number(process.env.MAX_EMAIL_ATTACH_SIZE) || 10 * 1024 * 1024,
    refreshTokenInterval: Number(process.env.REFRESH_TOKEN_INTERVAL) || 8 * 60 * 1000,
    enableFirefox: process.env.REACT_APP_ENABLE_FIREFOX === 'true',
    enableAlertRequest: process.env.REACT_APP_ENABLE_ALERT_REQUEST === 'true',
  };
};
