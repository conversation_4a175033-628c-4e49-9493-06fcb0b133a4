import React, { useEffect, useState } from 'react';
import { Box, Flex, Text } from '@mantine/core';
import { KanbanTabs, KanbanCheckbox, KanbanSelect } from 'kanban-design-system';
import { Execution } from '@core/schema/Execution';
import { AuthTypeEnum, BodyTypeEnum, AuthTypeLabel, HTTP_VERSIONS, ContentTypeLabel, ContentTypeEnum } from '@common/constants/ExecutionConstants';
import { beautifyRaw } from '@common/utils/BeautifyUtils';
import classes from '../../../admins/executionConfig/tabs/execution/ApiConfigSession.module.css';

const HighlightedText = ({ text }: { text: string }) => {
  if (!text) {
    return <Text size='sm'>-</Text>;
  }

  // Convert {{variable}} to @@variable and highlight
  const parts = text.split(/(\{\{[^}]+\}\})/g);

  return (
    <Text size='sm' style={{ wordBreak: 'break-all' }}>
      {parts.map((part, index) => {
        if (part.match(/^\{\{[^}]+\}\}$/)) {
          const variableName = part.slice(2, -2);
          return (
            <span
              key={index}
              style={{
                backgroundColor: 'var(--mantine-color-blue-1)',
                color: 'var(--mantine-color-blue-7)',
                padding: '2px 4px',
                borderRadius: '3px',
                fontWeight: 500,
              }}>
              @@{variableName}
            </span>
          );
        }
        return part;
      })}
    </Text>
  );
};

interface ReadOnlyApiConfigTabsProps {
  execution: Execution;
}

// Read-only table
const ReadOnlyTable = ({
  data,
  title,
}: {
  data: Array<{ key: string; value: string; description: string; enable: boolean; autoGenerated?: boolean }>;
  title: string;
}) => {
  const enabledData = data.filter((item) => item.enable && (item.key || item.value));

  if (enabledData.length === 0) {
    return (
      <Box className={classes.bodyContainer}>
        <Text size='sm' c='dimmed'>
          No {title.toLowerCase()}
        </Text>
      </Box>
    );
  }

  return (
    <Box className={classes.bodyContainer}>
      <Box className={classes.tableWrap}>
        {/* Table Header */}
        <Box className={classes.tableHeader}>
          <Box></Box>
          <Text size='sm' fw={600}>
            Key
          </Text>
          <Text size='sm' fw={600}>
            Value
          </Text>
          <Text size='sm' fw={600}>
            Description
          </Text>
          <Box></Box>
        </Box>

        {/* Table Rows */}
        {enabledData.map((item, index) => (
          <Box key={index} className={classes.tableRow}>
            <Box className={classes.checkboxContainer}>
              <KanbanCheckbox checked={item.enable} disabled={true} size='sm' />
            </Box>
            <Box className={classes.tableCell}>
              <HighlightedText text={item.key} />
            </Box>
            <Box className={classes.tableCell}>
              <HighlightedText text={item.value} />
            </Box>
            <Box className={classes.tableCell}>
              <Text size='sm' c='dimmed'>
                {item.description}
              </Text>
            </Box>
            <Box className={classes.actionContainer}>{/* Empty action cell for consistency */}</Box>
          </Box>
        ))}
      </Box>
    </Box>
  );
};

const ReadOnlyApiConfigTabs: React.FC<ReadOnlyApiConfigTabsProps> = ({ execution }) => {
  const { apiInfo } = execution;
  const [formattedBody, setFormattedBody] = useState<string>('');

  // Auto-beautify body content when component mounts
  useEffect(() => {
    const formatBody = async () => {
      if (apiInfo?.body?.bodyRaw && apiInfo?.body?.contentType) {
        try {
          const formatted = await beautifyRaw(apiInfo.body.bodyRaw, apiInfo.body.contentType);
          setFormattedBody(formatted);
        } catch (error) {
          setFormattedBody(apiInfo.body.bodyRaw);
        }
      } else {
        setFormattedBody(apiInfo?.body?.bodyRaw || '');
      }
    };
    formatBody();
  }, [apiInfo]);

  if (!apiInfo) {
    return <Text c='dimmed'>No API configuration available</Text>;
  }

  return (
    <KanbanTabs
      configs={{
        defaultValue: 'params',
        classNames: {
          root: classes.tabsRoot,
          list: classes.tabsList,
        },
      }}
      tabs={{
        params: {
          title: 'Params',
          content: <ReadOnlyTable data={apiInfo.params || []} title='Parameters' />,
        },
        auth: {
          title: 'Authorization',
          content: (
            <Box className={classes.bodyContainer}>
              <Box className={classes.authInlineContainer}>
                <Box className={classes.authTypeInlineSection}>
                  <Text size='sm' fw={500}>
                    Type
                  </Text>
                  <KanbanSelect
                    data={[
                      {
                        value: AuthTypeLabel[apiInfo.authentication?.authType || AuthTypeEnum.NONE],
                        label: AuthTypeLabel[apiInfo.authentication?.authType || AuthTypeEnum.NONE],
                      },
                    ]}
                    value={AuthTypeLabel[apiInfo.authentication?.authType || AuthTypeEnum.NONE]}
                    disabled={true}
                  />
                </Box>
                <Box className={classes.authFieldInlineSection}>
                  {apiInfo.authentication?.authType === AuthTypeEnum.BEARER && apiInfo.authentication?.token && (
                    <Box className={classes.authFieldInlineContainer}>
                      <Text size='sm' fw={500}>
                        Token
                      </Text>
                      <Box className={classes.inputBox}>
                        <HighlightedText text={apiInfo.authentication.token} />
                      </Box>
                    </Box>
                  )}

                  {apiInfo.authentication?.authType === AuthTypeEnum.BASIC && (
                    <>
                      {apiInfo.authentication?.username && (
                        <Flex className={classes.authFieldInlineContainer}>
                          <Text size='sm' fw={500}>
                            Username
                          </Text>
                          <Box className={classes.inputBox} style={{ flex: 1 }}>
                            <Text size='sm'>{apiInfo.authentication.username}</Text>
                          </Box>
                        </Flex>
                      )}
                      <Flex className={classes.authFieldInlineContainer}>
                        <Text size='sm' fw={500}>
                          Password
                        </Text>
                        <Box className={classes.inputBox} style={{ flex: 1 }}>
                          <Text size='sm'>{apiInfo.authentication?.password}</Text>
                        </Box>
                      </Flex>
                    </>
                  )}
                </Box>
              </Box>
            </Box>
          ),
        },
        headers: {
          title: 'Headers',
          content: <ReadOnlyTable data={apiInfo.headers || []} title='Headers' />,
        },
        body: {
          title: 'Body',
          content: (
            <Box className={classes.bodyContainer}>
              {/* Body Type Label */}
              <Text size='sm' fw={500}>
                Body Type
              </Text>
              <Flex gap='md' align='center'>
                <Flex gap='md' align='center'>
                  <Flex align='center' gap='xs'>
                    <input type='radio' checked={apiInfo.body?.bodyType === BodyTypeEnum.NONE} disabled />
                    <Text size='sm'>none</Text>
                  </Flex>
                  <Flex align='center' gap='xs'>
                    <input type='radio' checked={apiInfo.body?.bodyType === BodyTypeEnum.URLENCODED} disabled />
                    <Text size='sm'>x-www-form-urlencoded</Text>
                  </Flex>
                  <Flex align='center' gap='xs'>
                    <input type='radio' checked={apiInfo.body?.bodyType === BodyTypeEnum.RAW} disabled />
                    <Text size='sm'>raw</Text>
                  </Flex>
                </Flex>

                {apiInfo.body?.bodyType === BodyTypeEnum.RAW && (
                  <Flex gap='sm' align='center'>
                    {apiInfo.body?.contentType && (
                      <Box className={classes.contentTypeSelect}>
                        <KanbanSelect
                          data={[
                            {
                              value: ContentTypeLabel[apiInfo.body.contentType || ContentTypeEnum.TEXT],
                              label: ContentTypeLabel[apiInfo.body.contentType || ContentTypeEnum.TEXT],
                            },
                          ]}
                          value={ContentTypeLabel[apiInfo.body.contentType || ContentTypeEnum.TEXT]}
                          disabled={true}
                        />
                      </Box>
                    )}
                  </Flex>
                )}
              </Flex>

              {apiInfo.body?.bodyType === BodyTypeEnum.RAW && (
                <Box className={classes.textareaContainer}>
                  <Box
                    mih={200}
                    mah={400}
                    p='sm'
                    bg='gray.0'
                    style={{
                      overflow: 'auto',
                      border: '1px solid var(--mantine-color-gray-3)',
                      fontFamily: 'monospace',
                      fontSize: 'var(--mantine-font-size-sm)',
                      whiteSpace: 'pre-wrap',
                    }}>
                    <HighlightedText text={formattedBody || 'No content'} />
                  </Box>
                </Box>
              )}

              {apiInfo.body?.bodyType === BodyTypeEnum.URLENCODED && <ReadOnlyTable data={apiInfo.body?.formUrlEncoded || []} title='Form Data' />}
            </Box>
          ),
        },
        settings: {
          title: 'Settings',
          content: (
            <Box className={classes.bodyContainer}>
              <Box mb='lg'>
                <Flex justify='space-between' align='flex-start'>
                  <Box style={{ flex: 1 }}>
                    <Text size='sm' fw={500} mb='xs'>
                      HTTP Version
                    </Text>
                    <Text size='sm' c='dimmed'>
                      Select the HTTP version to use for sending the request
                    </Text>
                  </Box>
                  <Box style={{ miw: '120', textAlign: 'right' }}>
                    <KanbanSelect
                      data={[{ value: HTTP_VERSIONS[apiInfo.httpVersion], label: HTTP_VERSIONS[apiInfo.httpVersion] }]}
                      value={HTTP_VERSIONS[apiInfo.httpVersion]}
                      disabled={true}
                    />
                  </Box>
                </Flex>
              </Box>

              <Box>
                <Flex justify='space-between' align='flex-start'>
                  <Box style={{ flex: 1 }}>
                    <Text size='sm' fw={500} mb='xs'>
                      Enable SSL certificate verification
                    </Text>
                    <Text size='sm' c='dimmed'>
                      Verify SSL certificates when sending a request. Verification failures will result in the request being aborted.
                    </Text>
                  </Box>
                  <Box style={{ miw: '120', textAlign: 'right' }}>
                    <KanbanCheckbox checked={apiInfo.enableSsl} disabled={true} size='sm' />
                  </Box>
                </Flex>
              </Box>
            </Box>
          ),
        },
      }}
    />
  );
};

export default ReadOnlyApiConfigTabs;
