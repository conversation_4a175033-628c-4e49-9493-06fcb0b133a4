.container {
  width: 100%;
}

.label {
  display: block;
  font-size: var(--mantine-font-size-sm);
  font-weight: 500;
  color: var(--mantine-color-text);
  border-bottom: 2px solid transparent;
}

.required {
  color: var(--mantine-color-error);
  margin-left: 2px;
}

.editorWrapper {
  border: 1px solid var(--mantine-color-default-border);
  border-radius: var(--mantine-radius-default);
  min-height: 34px;
  background: var(--mantine-color-body);
  transition: border-color 150ms ease;

  &:focus-within {
    border-color: var(--mantine-color-blue-filled);
    box-shadow: 0 0 0 1px #228be6;
  }

  &.disabledWrapper {
    cursor: not-allowed;
    opacity: .6;
    background-color: var(--input-disabled-bg);
    color: var(--input-disabled-color);
  }
}

.editor {
  padding: 0.4rem 1rem;
  border-radius: 0.3rem;
  background-color: var(--mantine-color-white);
  border: 0.1rem solid var(--mantine-color-gray-4);
  font-size: 0.875rem;
  color:  var(--mantine-color-dark-6);
  outline: none;
  resize: none;
  transition: background-color 0.2s, color 0.2s, border-color 0.2s;
 
  &.disabled {
    background-color: var(--mantine-color-gray-1);
    color: var(--mantine-color-gray-6);
    border-color: var(--mantine-color-gray-3);
    cursor: not-allowed;
  }
}
 
.paragraph {
  margin: 0;
  line-height: 1.5;
  color: inherit;
}
 
.disabled .paragraph {
  color: var(--mantine-color-gray-6);
}
 
.mention {
  color: var(--mantine-color-blue-6);
  background-color: var(--mantine-color-white);
  padding: 0.2rem 0.5rem 0.2rem 0;
  border-radius: 0.5rem;
}
 
.disabled .mention {
  color: var(--mantine-color-gray-6);
  background-color: var(--mantine-color-gray-2);
}
