import React from 'react';
import { Control, UseFormSetValue, UseFormGetValues, UseFormSetFocus } from 'react-hook-form';
import { Box } from '@mantine/core';
import { ExecutionApiInfoModel } from '@models/ExecutionApiInfoModel';
import { RenderTable } from './RenderTable';

interface HeadersTabProps {
  control: Control<{ apiInfo: ExecutionApiInfoModel }>;
  setValue: UseFormSetValue<{ apiInfo: ExecutionApiInfoModel }>;
  getValues: UseFormGetValues<{ apiInfo: ExecutionApiInfoModel }>;
  setFocus?: UseFormSetFocus<{ apiInfo: ExecutionApiInfoModel }>;
  headersFA: {
    fields: any[];
    append: (value: any) => void;
    remove: (index: number) => void;
  };
  isViewMode?: boolean;
  variableNames?: string[];
  registerEditor?: (name: string, editor: any) => void;
  onFieldChange?: () => void;
}

const HeadersTab: React.FC<HeadersTabProps> = ({
  control,
  getValues,
  headersFA,
  isViewMode = false,
  onFieldChange,
  registerEditor,
  setFocus,
  setValue,
  variableNames = [],
}) => {
  return (
    <Box p='md'>
      <RenderTable
        fa={headersFA}
        control={control}
        namePrefix='apiInfo.headers'
        isViewMode={isViewMode}
        executionParams={variableNames}
        getValues={getValues}
        setValue={setValue}
        registerEditor={registerEditor}
        setFocus={setFocus}
        onFieldChange={onFieldChange}
      />
    </Box>
  );
};

export default HeadersTab;
