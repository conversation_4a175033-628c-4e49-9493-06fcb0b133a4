import { Box, Flex, TagsInput } from '@mantine/core';
import React, { useCallback, useEffect, useMemo } from 'react';
import DragTable from '@components/dragTable/DragTable';
import { Column, OnDragHandler } from '@components/dragTable/Types';
import { AclPermission } from '@models/AclPermission';
import { isAnyPermissions } from '@common/utils/AclPermissionUtils';
import { WebMonitorConfigModel } from '@models/WebMonitorConfigModel';
import { KanbanButton, KanbanIconButton, KanbanInput, KanbanNumberInput, KanbanSelect, KanbanTooltip } from 'kanban-design-system';
import { ACTION_TYPE_LABEL, ActionTypeEnum, FIND_ELEMENT_BY_LABEL, FindElementByEnum } from '@common/constants/WebMonitorConfigConstant';
import { Controller, FieldArrayWithId, useFieldArray, UseFormReturn, useWatch } from 'react-hook-form';
import { IconP<PERSON>, IconTrash } from '@tabler/icons-react';
import {
  INDENTIFIER_MAX_LENGTH,
  MAX_ENABLE_WEB_MONITOR_WEB_WAIT,
  MAX_OF_GO_TO_URL_ACTION_VALUE,
  MAX_OF_OTHERS_ACTION_VALUE,
  MAX_OF_SELECT_FROM_DROPDOWN_ACTION_VALUE,
  NOTE_MAX_LENGTH,
  WEB_MONITOR_WEB_WAIT_MAX_VALUE,
  WEB_MONITOR_WEB_WAIT_MIN_VALUE,
} from '@common/constants/ValidationConstant';
import { IDENTIFIER_SHOULD_DISABLE, VALUE_SHOULD_ENABLE, VALUE_SHOULD_REQUIRED, WAIT_TYPE } from '@common/constants/MonitorActionRules';

interface Props {
  form: UseFormReturn<WebMonitorConfigModel>;
  isViewMode?: boolean;
}

type FieldType = FieldArrayWithId<WebMonitorConfigModel, 'actions', 'fieldId'>;

const ActionSession = ({ form, isViewMode }: Props) => {
  const { control } = form;

  const { fields, insert, move, remove } = useFieldArray({
    control,
    name: 'actions',
    keyName: 'fieldId',
  });

  const watchedActionList = useWatch({
    control,
    name: 'actions',
  });

  useEffect(() => {
    watchedActionList?.forEach((action, index) => {
      const type = action.actionType;

      const shouldClearValue = !VALUE_SHOULD_ENABLE.includes(type);
      const shouldClearIdentifier = IDENTIFIER_SHOULD_DISABLE.includes(type);

      if (shouldClearValue && action.value) {
        form.setValue(`actions.${index}.value`, '', {
          shouldDirty: true,
          shouldValidate: false,
        });
      }

      if (shouldClearIdentifier && action.identifier) {
        form.setValue(`actions.${index}.identifier`, '', {
          shouldDirty: true,
          shouldValidate: false,
        });
      }
    });
  }, [watchedActionList, form]);

  const columns = useMemo<Column<FieldType>[]>(
    () => [
      {
        id: 'actionType',
        title: 'Action Type',
        render: (record) => {
          const index = fields.findIndex((item) => item.fieldId === record.fieldId);
          return (
            <Controller
              name={`actions.${index}.actionType`}
              control={control}
              render={({ field, fieldState }) => (
                <Flex direction='column' justify='flex-start' pt='xs' h={48}>
                  <KanbanSelect
                    {...field}
                    disabled={isViewMode}
                    required
                    data={Object.values(ActionTypeEnum).map((key) => ({
                      value: key,
                      label: ACTION_TYPE_LABEL[key],
                    }))}
                    defaultValue={record?.actionType ?? ActionTypeEnum.CLICK}
                    allowDeselect={false}
                    searchable
                    error={fieldState.error?.message}
                  />
                </Flex>
              )}
            />
          );
        },
        width: '15%',
      },
      {
        id: 'findElementBy',
        title: 'Find Element By',
        render: (record) => {
          const index = fields.findIndex((item) => item.fieldId === record.fieldId);
          const currentActionType = watchedActionList?.[index]?.actionType;
          const shouldDisable = IDENTIFIER_SHOULD_DISABLE.includes(currentActionType);
          return (
            <Controller
              name={`actions.${index}.findElementBy`}
              control={control}
              render={({ field, fieldState }) => (
                <Flex direction='column' justify='flex-start' pt='xs' h={48}>
                  <KanbanSelect
                    {...field}
                    disabled={isViewMode || shouldDisable}
                    required
                    data={Object.values(FindElementByEnum).map((key) => ({ value: key, label: FIND_ELEMENT_BY_LABEL[key] }))}
                    defaultValue={record?.findElementBy ?? FindElementByEnum.XPATH}
                    allowDeselect={false}
                    error={fieldState.error?.message}
                  />
                </Flex>
              )}
            />
          );
        },
        width: '15%',
      },
      {
        id: 'identifier',
        title: 'Identifier',
        render: (record) => {
          const index = fields.findIndex((item) => item.fieldId === record.fieldId);
          const currentActionType = watchedActionList?.[index]?.actionType;
          const shouldDisable = IDENTIFIER_SHOULD_DISABLE.includes(currentActionType);
          return (
            <Controller
              name={`actions.${index}.identifier`}
              control={control}
              render={({ field, fieldState }) => (
                <Flex direction='column' justify='flex-start' pt='xs' h={48}>
                  <KanbanInput
                    {...field}
                    disabled={isViewMode || shouldDisable}
                    required={!shouldDisable}
                    maxLength={INDENTIFIER_MAX_LENGTH}
                    error={fieldState.error?.message}
                  />
                </Flex>
              )}
            />
          );
        },
        width: '20%',
      },
      {
        id: 'value',
        title: 'Value',
        render: (record) => {
          const index = fields.findIndex((item) => item.fieldId === record.fieldId);
          const currentActionType = watchedActionList?.[index]?.actionType;
          const shouldEnable = VALUE_SHOULD_ENABLE.includes(currentActionType);
          const shouldRequired = VALUE_SHOULD_REQUIRED.includes(currentActionType);
          const isSelectDropdown = currentActionType === ActionTypeEnum.SELECT_FROM_DROPDOWN;
          const isWaitType = WAIT_TYPE.includes(currentActionType);
          const maxLength = currentActionType === ActionTypeEnum.GO_TO_URL ? MAX_OF_GO_TO_URL_ACTION_VALUE : MAX_OF_OTHERS_ACTION_VALUE;

          if (isSelectDropdown) {
            return (
              <Controller
                name={`actions.${index}.value`}
                control={control}
                render={({ field, fieldState }) => (
                  <Flex direction='column' justify='flex-start' pt='xs' h={48}>
                    <TagsInput
                      {...field}
                      value={typeof field.value === 'string' ? field.value.split(',').filter((s) => s.trim() !== '') : []}
                      onChange={(val) => field.onChange(val.join(','))}
                      onBlur={field.onBlur}
                      disabled={isViewMode || !shouldEnable}
                      maxLength={MAX_OF_SELECT_FROM_DROPDOWN_ACTION_VALUE}
                      required={shouldEnable}
                      placeholder='Enter text...'
                      acceptValueOnBlur
                      error={fieldState.error?.message}
                      styles={{ input: { maxHeight: '48px', overflowY: 'auto' } }}
                    />
                  </Flex>
                )}
              />
            );
          }

          if (isWaitType) {
            return (
              <Controller
                name={`actions.${index}.value`}
                control={control}
                render={({ field, fieldState: { error } }) => {
                  return (
                    <Flex direction='column' justify='flex-start' pt='xs' h={48}>
                      <KanbanNumberInput
                        max={MAX_ENABLE_WEB_MONITOR_WEB_WAIT}
                        allowNegative={false}
                        allowDecimal={false}
                        clampBehavior='strict'
                        disabled={isViewMode}
                        required
                        value={field.value ? Number(field.value) : undefined}
                        onChange={(val) => field.onChange(val?.toString() ?? '')}
                        onBlur={() => {
                          const raw = field.value;
                          const val = Number(field.value);
                          if (raw === '' || raw === undefined) {
                            field.onChange('1');
                          } else if (isNaN(val) || val < WEB_MONITOR_WEB_WAIT_MIN_VALUE) {
                            field.onChange(WEB_MONITOR_WEB_WAIT_MIN_VALUE.toString());
                          } else if (val > WEB_MONITOR_WEB_WAIT_MAX_VALUE) {
                            field.onChange(WEB_MONITOR_WEB_WAIT_MAX_VALUE.toString());
                          }
                        }}
                        error={error?.message}
                      />
                    </Flex>
                  );
                }}
              />
            );
          }

          return (
            <Controller
              name={`actions.${index}.value`}
              control={control}
              render={({ field, fieldState }) => (
                <Flex direction='column' justify='flex-start' pt='xs' h={48}>
                  <KanbanInput
                    {...field}
                    disabled={isViewMode || !shouldEnable}
                    required={shouldRequired}
                    maxLength={maxLength}
                    error={fieldState.error?.message}
                  />
                </Flex>
              )}
            />
          );
        },
        width: '22%',
      },
      {
        id: 'note',
        title: 'Note',
        render: (record) => {
          const index = fields.findIndex((item) => item.fieldId === record.fieldId);
          return (
            <Controller
              name={`actions.${index}.note`}
              control={control}
              render={({ field, fieldState }) => (
                <Flex direction='column' justify='flex-start' pt='xs' h={48}>
                  <KanbanInput {...field} disabled={isViewMode} maxLength={NOTE_MAX_LENGTH} error={fieldState.error?.message} />
                </Flex>
              )}
            />
          );
        },
        width: '15%',
      },
      {
        id: 'actions',
        title: '',
        render: (record) => (
          <KanbanTooltip label='Delete'>
            <Flex direction='column' justify='flex-start' pt={10} h={36}>
              <KanbanIconButton
                variant='transparent'
                size='sm'
                disabled={isViewMode || fields.length === 1}
                onClick={() => {
                  const index = fields.findIndex((item) => item.fieldId === record.fieldId);
                  if (index !== -1) {
                    remove(index);
                  }
                }}>
                <IconTrash color='red' />
              </KanbanIconButton>
            </Flex>
          </KanbanTooltip>
        ),
        width: '5%',
      },
    ],
    [isViewMode, remove, fields, control, watchedActionList],
  );

  const updatePositionHandler = useCallback<OnDragHandler<FieldType>>(
    (active, over) => {
      if (!active || !over || active.fieldId === over.fieldId) {
        return;
      }

      const fromIndex = fields.findIndex((item) => item.fieldId === active.fieldId);
      const toIndex = fields.findIndex((item) => item.fieldId === over.fieldId);

      if (fromIndex === -1 || toIndex === -1) {
        return;
      }

      move(fromIndex, toIndex);
    },
    [fields, move],
  );

  const handleAddAction = () => {
    insert(fields.length, {
      actionType: ActionTypeEnum.CLICK,
      findElementBy: FindElementByEnum.XPATH,
      identifier: '',
      value: '',
    });
  };

  return (
    <Box>
      <Box px='xs' py='xs'>
        <DragTable
          disableDraggable={!isAnyPermissions([AclPermission.webMonitorConfigEdit])}
          columns={columns}
          data={fields}
          onDragHandler={updatePositionHandler}
          showIndexColumn={true}
          dataKey={(record) => record.fieldId}
        />
      </Box>
      {!isViewMode && (
        <Flex direction='row' gap='xs' align='center' mt='xs'>
          <KanbanButton size='xs' disabled={isViewMode} onClick={handleAddAction} leftSection={<IconPlus />}>
            Add Action
          </KanbanButton>
        </Flex>
      )}
    </Box>
  );
};

export default ActionSession;
