import Document from '@tiptap/extension-document';
import Mention from '@tiptap/extension-mention';
import Paragraph from '@tiptap/extension-paragraph';
import Text from '@tiptap/extension-text';
import { EditorContent, JSONContent, React<PERSON><PERSON>er, useEditor } from '@tiptap/react';
import React, { useEffect, useRef, useState } from 'react';
import '@mantine/tiptap/styles.css';
import styles from './CustomVariableInput.module.scss';
import tippy, { GetReferenceClientRect, Instance, Props } from 'tippy.js';
import CharacterCount from '@tiptap/extension-character-count';
import { VariableApi } from '@api/VariableApi';
import useFetch from '@core/hooks/useFetch';
import { VariableMentionListActions, VariableMentionListProps, VariableMentionList } from './VariableMentionList';
import { Box, InputError, InputLabel } from '@mantine/core';
import { MAX_OF_OTHERS_ACTION_VALUE } from '@common/constants/ValidationConstant';

interface EditorProps {
  value: string;
  onChange: (content: string, value?: string) => void;
  label?: string;
  disabled?: boolean;
  required?: boolean;
  maxLength?: number;
  error?: string;
}

// For backend - format as {{variable}}
function formatContentToBackend(content: JSONContent[]): string {
  return content
    .map((node) => {
      if (node.type === 'text' && node.text) {
        return node.text;
      } else if (node.type === 'mention' && node.attrs?.id && node.attrs?.label) {
        return `{{${node.attrs.id}}}`;
      } else if (node.content && Array.isArray(node.content)) {
        return formatContentToBackend(node.content);
      }
      return '';
    })
    .join('');
}

function parseStringToJson(str: string): JSONContent {
  // Handle non-string values (numbers, null, undefined, etc.)
  if (typeof str !== 'string') {
    if (str === null || str === undefined) {
      str = '';
    } else {
      str = String(str); // Convert to string
    }
  }

  // Handle empty string
  if (!str || str.trim() === '') {
    return {
      type: 'doc',
      content: [
        {
          type: 'paragraph',
          content: [],
        },
      ],
    };
  }

  try {
    // Try to parse as JSON first (for editor content)
    const parsed = JSON.parse(str);
    if (parsed && typeof parsed === 'object' && parsed.type === 'doc') {
      return parsed;
    }
    // If it's valid JSON but not editor format, treat as plain text
    return parseTextWithVariables(str);
  } catch (e) {
    // If not JSON, parse as text with {{ variable }} patterns
    return parseTextWithVariables(str);
  }
}

function parseTextWithVariables(text: string): JSONContent {
  const content: JSONContent[] = [];
  const variablePattern = /\{\{\s*([^}]+)\s*\}\}/g;
  let lastIndex = 0;
  let match;

  while ((match = variablePattern.exec(text)) !== null) {
    // Add text before the variable
    if (match.index > lastIndex) {
      const textBefore = text.slice(lastIndex, match.index);
      if (textBefore) {
        content.push({
          type: 'text',
          text: textBefore,
        });
      }
    }

    // Add the variable as a mention
    content.push({
      type: 'mention',
      attrs: {
        id: match[1].trim(),
        label: match[1].trim(),
      },
    });

    lastIndex = match.index + match[0].length;
  }

  // Add remaining text
  if (lastIndex < text.length) {
    const remainingText = text.slice(lastIndex);
    if (remainingText) {
      content.push({
        type: 'text',
        text: remainingText,
      });
    }
  }

  return {
    type: 'doc',
    content: [
      {
        type: 'paragraph',
        content: content.length > 0 ? content : [{ type: 'text', text: text }],
      },
    ],
  };
}

const DEFAULT_MAX_LENGTH = 500;

export const CustomVariableInput: React.FC<EditorProps> = ({ disabled = false, error, label, maxLength = DEFAULT_MAX_LENGTH, onChange, value }) => {
  const hasHydrated = useRef(false);
  const [errorMessage, setErrorMessage] = useState<string>('');

  // Load variables from API
  const { refetch } = useFetch(VariableApi.findAll(), { showLoading: false });

  // Force refetch on mount
  useEffect(() => {
    refetch();
  }, [refetch]);

  const editor = useEditor({
    editable: !disabled,
    content: parseStringToJson(value),
    editorProps: {
      attributes: {
        class: `${styles.editor} ${disabled ? styles.disabled : ''}`,
      },
      handleDOMEvents: {
        paste: (view, event: ClipboardEvent) => {
          if (!editor) {
            return false;
          }
          const currentContentLength = editor.storage.characterCount.characters();
          const pastedText = event.clipboardData?.getData('text/plain') || '';
          const pastedLength = pastedText.length;
          if (currentContentLength + pastedLength > maxLength) {
            const remainingLength = maxLength - currentContentLength;
            if (remainingLength <= 0) {
              event.preventDefault();
              return true;
            }
            const truncatedText = pastedText.slice(0, remainingLength);
            const insertPos = view.state.selection.from;

            editor.commands.insertContentAt(insertPos, truncatedText);
            event.preventDefault();
            return true;
          }
          return false;
        },
        beforeinput: (view, event: InputEvent) => {
          if (!editor) {
            return false;
          }
          const json = editor.getJSON();
          const currentLength = formatContentToBackend(json.content as JSONContent[]).length;
          const inputData = (event as InputEvent).data || '';
          const selectionLength = view.state.selection.to - view.state.selection.from;

          const newLength = currentLength - selectionLength + inputData.length;

          if (newLength > maxLength) {
            event.preventDefault();
            setErrorMessage(`Content cannot exceed ${maxLength} characters.`);
            setTimeout(() => setErrorMessage(''), 3000);
            return true;
          }
          // Clear any existing error message
          setErrorMessage('');
          return false;
        },
      },
    },
    extensions: [
      Document,
      Paragraph,
      Text,
      CharacterCount.configure({
        limit: maxLength,
      }),
      Mention.configure({
        HTMLAttributes: {
          class: styles.mention,
        },
        suggestion: {
          char: '@@',
          command: ({ editor, props, range }) => {
            const { from } = range;
            editor
              .chain()
              .focus()
              .deleteRange(range)
              .insertContentAt(from, {
                type: 'mention',
                attrs: props,
              })
              .run();
          },
          render: () => {
            let component: ReactRenderer<VariableMentionListActions, VariableMentionListProps>;
            let popup: Instance<Props>[];

            return {
              onStart({ clientRect, editor, ...restProps }) {
                component = new ReactRenderer(VariableMentionList, {
                  props: { editor, clientRect, ...restProps },
                  editor,
                });

                if (!clientRect) {
                  return;
                }

                popup = tippy('body', {
                  getReferenceClientRect: clientRect as GetReferenceClientRect,
                  appendTo: () => document.body,
                  content: component.element,
                  showOnCreate: true,
                  interactive: true,
                  trigger: 'manual',
                  placement: 'bottom-start',
                });
              },

              onUpdate({ clientRect, ...restProps }) {
                component.updateProps({ clientRect, ...restProps });

                if (!clientRect) {
                  return;
                }

                popup[0].setProps({
                  getReferenceClientRect: clientRect as GetReferenceClientRect,
                });
              },

              onKeyDown({ event, ...restProps }) {
                if (event.key === 'Escape') {
                  popup[0].hide();

                  return true;
                }

                return component.ref?.onKeyDown({ event, ...restProps }) || false;
              },

              onExit() {
                popup[0].destroy();
                component.destroy();
              },
            };
          },
        },
      }),
    ],
    onUpdate: ({ editor }) => {
      const json = editor.getJSON();
      const backendText = formatContentToBackend(json.content as JSONContent[]);
      onChange(JSON.stringify(json), backendText);
    },
  });

  useEffect(() => {
    if (editor && !hasHydrated.current) {
      hasHydrated.current = true;
      const json = editor.getJSON();
      const backendText = formatContentToBackend(json.content as JSONContent[]);
      onChange(JSON.stringify(json), backendText);
    }
  }, [editor, onChange]);

  // Update editor content when value prop changes (for data from BE)
  useEffect(() => {
    if (editor && hasHydrated.current) {
      const currentContent = JSON.stringify(editor.getJSON());
      const newContent = JSON.stringify(parseStringToJson(value));

      // Only update if content actually changed to avoid infinite loops
      if (currentContent !== newContent) {
        editor.commands.setContent(parseStringToJson(value));
      }
    }
  }, [editor, value]);

  return (
    <Box className={styles.container}>
      {label && (
        <InputLabel size='sm' fw={MAX_OF_OTHERS_ACTION_VALUE} required className={styles.label}>
          {label}
        </InputLabel>
      )}
      <Box className={`${styles.editorWrapper} ${disabled ? styles.disabledWrapper : ''}`}>
        <EditorContent editor={editor} />
      </Box>
      {(errorMessage || error) && <InputError>{errorMessage || error}</InputError>}
    </Box>
  );
};
