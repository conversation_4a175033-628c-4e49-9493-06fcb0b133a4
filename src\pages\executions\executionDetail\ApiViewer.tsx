import React, { useState, useCallback, useEffect } from 'react';
import { ActionIcon, Box, Code, Collapse, Flex, Stack, Title, Group, Text, Loader } from '@mantine/core';
import { Execution } from '@core/schema/Execution';
import { ExecutionTypeEnum, METHOD_COLORS, MethodTypeEnum } from '@common/constants/ExecutionConstants';
import { KanbanInput, KanbanSelect, KanbanButton } from 'kanban-design-system';
import { IconChevronDown, IconGlobe, IconPlayerPlayFilled } from '@tabler/icons-react';
import { useDisclosure } from '@mantine/hooks';
import { VARIABLE_MAX_LENGTH } from '@common/constants/ValidationConstant';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { ExecuteScriptModel, ExecuteScriptModelSchema } from '@models/ExecuteScriptModel';
import { ExecutionApi } from '@api/ExecutionApi';
import useMutate from '@core/hooks/useMutate';
import ApiResult from './ApiResult';
import { QueryApiExecute } from '@core/schema/QueryApiExecute';
import classes from '../../admins/executionConfig/tabs/execution/ApiConfigSession.module.css';
import { AxiosError } from 'axios';
import { NotificationError, NotificationWarning } from '@common/utils/NotificationUtils';
import { EXECUTION_IS_RUNNING_LONGER_THAN_EXPECTED_ERROR_CODE, EXECUTION_UPDATED_ERROR_CODE } from '../Constants';
import { defaultErrorNotification } from '@core/hooks/Utils';
import { EXECUTION_API_PARAM_VALUE_PREFIX } from '@pages/admins/executionConfig/Constants';
import ApiConfig from '@components/executionApiForm/ApiConfig';
import { ExecutionApiInfoModel } from '@models/ExecutionApiInfoModel';
import { useApiInfoForm } from '@models/ExecutionModel';

export const extractApiInfoFromExecution = (execution: ExecutionModel): ExecutionApiInfoModel | undefined => {
  return execution.apiInfo;
};

export const mergeApiInfoToExecution = (execution: ExecutionModel, apiInfo: ExecutionApiInfoModel): ExecutionModel => {
  return {
    ...execution,
    apiInfo,
  };
};

export const syncApiInfoBetweenForms = (sourceForm: any, targetForm: any, fieldPath: string = 'apiInfo') => {
  const apiInfo = sourceForm.getValues(fieldPath);
  targetForm.setValue(fieldPath, apiInfo);
};

const HighlightedText = ({ text }: { text: string }) => {
  if (!text) {
    return <Text size='sm'>-</Text>;
  }

  const parts = text.split(/(\{\{[^}]+\}\})/g);

  return (
    <Text size='sm' style={{ wordBreak: 'break-all', color: 'black' }}>
      {parts.map((part, index) => {
        if (part.match(/^\{\{[^}]+\}\}$/)) {
          const variableName = part.slice(2, -2);
          return (
            <span
              key={index}
              style={{
                backgroundColor: 'var(--mantine-color-blue-1)',
                color: 'var(--mantine-color-blue-7)',
                padding: '2px 4px',
                borderRadius: '3px',
                fontWeight: 500,
              }}>
              {`${EXECUTION_API_PARAM_VALUE_PREFIX}${variableName}`}
            </span>
          );
        }
        return part;
      })}
    </Text>
  );
};

interface Props {
  execution: Execution;
}

// Method + URL Input component (read-only version of ApiMethodUrlInput)
const ReadOnlyMethodUrlInput = ({ execution }: { execution: Execution }) => {
  const { apiInfo } = execution;

  if (!apiInfo) {
    return null;
  }

  const MethodOptions = Object.values(MethodTypeEnum).map((m) => ({
    label: m,
    value: m,
  }));

  return (
    <Group className={classes.methodUrlRow}>
      <KanbanSelect
        data={MethodOptions}
        value={apiInfo.method || MethodTypeEnum.GET}
        disabled={true}
        className={classes.methodSelect}
        styles={{
          input: {
            color: METHOD_COLORS[apiInfo.method || MethodTypeEnum.GET],
            backgroundColor: 'white',
            opacity: 1,
          },
        }}
      />
      <Box className={`${classes.inputBox} ${classes.isUrl}`}>
        <HighlightedText text={apiInfo.url || ''} />
      </Box>
    </Group>
  );
};

const ApiExecutionViewer = ({ execution }: Props) => {
  const [opened, { toggle }] = useDisclosure(true);
  const { description, variables } = execution;
  const [result, setResult] = useState<QueryApiExecute | null>(null);
  const [isExecuting, setIsExecuting] = useState(false);

  // Form for variables
  const form = useForm<ExecuteScriptModel>({
    defaultValues: {
      executionId: execution.id,
      name: execution.name,
      description: execution.description,
      type: execution.type,
      executionGroupId: execution.executionGroupId,
      databaseConnectionId: execution.databaseConnectionId,
      script: execution.script,
      apiInfo: execution.apiInfo,
      variables:
        variables?.map((variable) => ({
          id: variable.id,
          name: variable.name,
          value: variable.value || '',
        })) || [],
    },
    resolver: zodResolver(ExecuteScriptModelSchema),
  });

  const { formState, getValues, register } = form;

  // API execute mutation
  const { isPending, mutate: executeMutate } = useMutate(ExecutionApi.execute, {
    showLoading: false,
    errorNotification: { enable: false },
    onSuccess: (data) => {
      if (data.data && data.data.apiExecutionResponse) {
        setResult(data.data.apiExecutionResponse);
      }
    },
    onError(error) {
      if (error instanceof AxiosError) {
        if (error.code === EXECUTION_UPDATED_ERROR_CODE) {
          NotificationWarning({ title: 'Reload execution information', message: 'Execution info has been changed!' });
          return;
        } else if (error.code === EXECUTION_IS_RUNNING_LONGER_THAN_EXPECTED_ERROR_CODE) {
          NotificationWarning({
            title: 'Execution is running longer than expected.',
            message: 'Can view the results upon completion on the execution history',
          });
          return;
        }
      }
      NotificationError(defaultErrorNotification(error));
    },
  });

  useEffect(() => {
    setIsExecuting(isPending);
  }, [isPending]);

  const onExecuteClick = useCallback(() => {
    const formValues = getValues();
    executeMutate(formValues);
  }, [executeMutate, getValues]);

  if (execution.type !== ExecutionTypeEnum.API) {
    return null;
  }

  // Tạo form riêng cho apiInfo
  const apiInfoForm = useApiInfoForm({
    defaultValues: execution.apiInfo,
  });

  // Field arrays for ApiConfig
  const headersFA = useFieldArray({
    control: apiInfoForm.control,
    name: 'apiInfo.headers',
  });

  const paramsFA = useFieldArray({
    control: apiInfoForm.control,
    name: 'apiInfo.params',
  });

  const formFA = useFieldArray({
    control: apiInfoForm.control,
    name: 'apiInfo.body.formUrlEncoded',
  });

  return (
    <Stack gap='xs' p='xs' h='var(--kanban-appshell-maxheight-content)'>
      <Stack gap='xs' style={{ border: '1px solid var(--mantine-color-gray-3)', borderRadius: 'var(--mantine-radius-xs)' }} p='xs'>
        <Flex align='center' justify='space-between'>
          <Flex align='center' justify='flex-start' gap='md'>
            <Box w={24} h={24}>
              <IconGlobe size={24} color='var(--mantine-color-orange-5)' />
            </Box>

            <Title order={4} c='primary'>
              {execution.name}

              {description && <Code ml='sm'>{description}</Code>}
            </Title>
          </Flex>

          <ActionIcon onClick={toggle} variant='outline' size='md'>
            <IconChevronDown size={20} />
          </ActionIcon>
        </Flex>

        <Collapse in={opened}>
          <Stack gap='sm'>
            <ApiConfig
              form={apiInfoForm}
              headersFA={headersFA}
              paramsFA={paramsFA}
              formFA={formFA}
              isViewMode={true}
              variableNames={execution.variables?.map((v) => v.name) || []}
            />

            {/* Variables section */}
            {variables && variables.length > 0 && (
              <Stack gap='xs'>
                {variables.map((variable, index) => (
                  <KanbanInput
                    key={variable.id || index}
                    label={variable.name}
                    {...register(`variables.${index}.value`)}
                    maxLength={VARIABLE_MAX_LENGTH}
                  />
                ))}
              </Stack>
            )}
          </Stack>
        </Collapse>
      </Stack>

      {/* Execute Button Section */}
      <Box
        className={classes.executeSection}
        p='sm'
        style={{
          border: '1px solid var(--mantine-color-gray-3)',
          borderRadius: 'var(--mantine-radius-xs)',
          backgroundColor: 'var(--mantine-color-gray-0)',
        }}>
        <Flex justify='space-between' align='center'>
          <KanbanButton fullWidth={false} disabled={!formState.isValid || isExecuting} onClick={onExecuteClick}>
            <Box mr='xs'>{isExecuting ? <Loader size='sm' /> : <IconPlayerPlayFilled size={16} />}</Box>
            {isExecuting ? 'Executing' : 'Execute'}
          </KanbanButton>
        </Flex>
      </Box>

      {/* Result Section */}
      {result && (
        <Box style={{ flex: 1, overflowY: 'auto' }}>
          <ApiResult result={result} />
        </Box>
      )}
    </Stack>
  );
};

export default ApiExecutionViewer;
