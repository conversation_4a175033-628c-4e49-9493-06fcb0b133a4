import React from 'react';
import { Controller, UseFormReturn } from 'react-hook-form';
import { Flex } from '@mantine/core';
import { WebMonitorConfigModel } from '@models/WebMonitorConfigModel';
import { KanbanMultiSelect } from 'kanban-design-system';
import { dayOptions, hourOptions, minuteOptions, monthOptions, weekOptions } from '@common/constants/WebMonitorConfigConstant';

interface Props {
  form: UseFormReturn<WebMonitorConfigModel>;
  isViewMode?: boolean;
}

const IntervalSession = ({ form, isViewMode }: Props) => {
  const { control } = form;
  return (
    <Flex direction='column' gap='sm'>
      <form>
        <Controller
          name='months'
          control={control}
          render={({ field, fieldState: { error } }) => (
            <KanbanMultiSelect
              label='Month'
              placeholder={(field.value ?? []).length === 0 ? 'Every month' : undefined}
              data={monthOptions}
              disabled={isViewMode}
              searchable
              error={error?.message}
              {...field}
              value={[...(field.value ?? [])].sort((a, b) => a - b).map(String)}
              onChange={(values) => field.onChange(values.map(Number).sort((a, b) => a - b))}
            />
          )}
        />
        <Controller
          name='dayOfMonths'
          control={control}
          render={({ field, fieldState: { error } }) => (
            <KanbanMultiSelect
              label='Day of month'
              placeholder={(field.value ?? []).length === 0 ? 'Every day of month' : undefined}
              data={dayOptions}
              disabled={isViewMode}
              searchable
              error={error?.message}
              {...field}
              value={[...(field.value ?? [])].sort((a, b) => a - b).map(String)}
              onChange={(values) => field.onChange(values.map(Number).sort((a, b) => a - b))}
            />
          )}
        />

        <Controller
          name='dayOfWeeks'
          control={control}
          render={({ field, fieldState: { error } }) => (
            <KanbanMultiSelect
              label='Day of week'
              placeholder={(field.value ?? []).length === 0 ? 'Every day of week' : undefined}
              data={weekOptions}
              disabled={isViewMode}
              searchable
              error={error?.message}
              {...field}
              value={[...(field.value ?? [])].sort((a, b) => a - b).map(String)}
              onChange={(values) => field.onChange(values.map(Number).sort((a, b) => a - b))}
            />
          )}
        />

        <Controller
          name='hours'
          control={control}
          render={({ field, fieldState: { error } }) => (
            <KanbanMultiSelect
              label='Hour'
              placeholder={(field.value ?? []).length === 0 ? 'Every hour' : undefined}
              data={hourOptions}
              disabled={isViewMode}
              searchable
              error={error?.message}
              {...field}
              value={[...(field.value ?? [])].sort((a, b) => a - b).map(String)}
              onChange={(values) => field.onChange(values.map(Number).sort((a, b) => a - b))}
            />
          )}
        />

        <Controller
          name='minutes'
          control={control}
          render={({ field, fieldState: { error } }) => (
            <KanbanMultiSelect
              label='Minute'
              placeholder={(field.value ?? []).length === 0 ? 'Every minute' : undefined}
              data={minuteOptions}
              disabled={isViewMode}
              searchable
              error={error?.message}
              {...field}
              value={[...(field.value ?? [])].sort((a, b) => a - b).map(String)}
              onChange={(values) => field.onChange(values.map(Number).sort((a, b) => a - b))}
            />
          )}
        />
      </form>
    </Flex>
  );
};

export default IntervalSession;
