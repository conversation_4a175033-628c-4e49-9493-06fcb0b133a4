import React from 'react';
import { Controller, UseFormReturn } from 'react-hook-form';
import { Flex, Grid, Group, Radio } from '@mantine/core';
import { KanbanInput, KanbanNumberInput, KanbanSelect } from 'kanban-design-system';
import {
  DESCRIPTION_MAX_LENGTH,
  MAX_ENABLE_WEB_MONITOR_WEB_TIMEOUT,
  NAME_MAX_LENGTH,
  WEB_MONITOR_WEB_TIMEOUT_MAX_VALUE,
  WEB_MONITOR_WEB_TIMEOUT_MIN_VALUE,
  WEB_MONITOR_WEB_URL_MAX_LENGTH,
} from '@common/constants/ValidationConstant';
import { WebMonitorConfigModel } from '@models/WebMonitorConfigModel';
import { ActionTypeEnum, BrowserEnum, FindElementByEnum, MONITOR_TYPE_LABEL, MonitorTypeEnum } from '@common/constants/WebMonitorConfigConstant';
import { getConfigs } from '@core/configs/Configs';

interface Props {
  form: UseFormReturn<WebMonitorConfigModel>;
  isViewMode?: boolean;
  isDiscoverMode?: boolean;
}
const enableFirefox = getConfigs().enableFirefox;

export const defaultAuthList = [
  {
    actionType: ActionTypeEnum.SEND_KEY,
    findElementBy: FindElementByEnum.XPATH,
    identifier: '',
    value: '',
    orders: 1,
  },
  {
    actionType: ActionTypeEnum.SEND_KEY,
    findElementBy: FindElementByEnum.XPATH,
    identifier: '',
    value: '',
    orders: 2,
  },
  {
    actionType: ActionTypeEnum.CLICK,
    findElementBy: FindElementByEnum.XPATH,
    identifier: '',
    orders: 3,
  },
];

const BaseInformationSession = ({ form, isViewMode }: Props) => {
  const { control } = form;

  return (
    <Flex direction='column' gap='sm'>
      <form>
        <Controller
          name='name'
          control={control}
          render={({ field, fieldState }) => (
            <KanbanInput
              label='Config name'
              disabled={isViewMode}
              maxLength={NAME_MAX_LENGTH}
              required
              {...field}
              error={fieldState.error?.message}
            />
          )}
        />
        <Controller
          name='description'
          control={control}
          render={({ field, fieldState }) => (
            <KanbanInput disabled={isViewMode} label='Description' maxLength={DESCRIPTION_MAX_LENGTH} {...field} error={fieldState.error?.message} />
          )}
        />
        <Controller
          name='webUrl'
          control={control}
          render={({ field, fieldState }) => (
            <KanbanInput
              disabled={isViewMode}
              label='Web URL'
              maxLength={WEB_MONITOR_WEB_URL_MAX_LENGTH}
              required
              {...field}
              error={fieldState.error?.message}
            />
          )}
        />
        <Grid>
          <Grid.Col span={3}>
            <Controller
              name='monitorType'
              control={control}
              render={({ field, fieldState }) => (
                <KanbanSelect
                  label='Monitor Type'
                  required
                  disabled={isViewMode}
                  data={Object.values(MonitorTypeEnum).map((key) => ({ value: key, label: MONITOR_TYPE_LABEL[key] }))}
                  defaultValue={MonitorTypeEnum.DISCOVER}
                  allowDeselect={false}
                  {...field}
                  onChange={(value) => {
                    field.onChange(value);
                    if (value === MonitorTypeEnum.DISCOVER) {
                      form.setValue('authActions', []);
                    } else {
                      form.setValue('authActions', defaultAuthList);
                    }
                  }}
                  error={fieldState.error?.message}
                />
              )}
            />
          </Grid.Col>
          <Grid.Col span={3}>
            <Controller
              name='timeout'
              control={control}
              render={({ field, fieldState: { error } }) => (
                <KanbanNumberInput
                  label='Timeout'
                  max={MAX_ENABLE_WEB_MONITOR_WEB_TIMEOUT}
                  allowNegative={false}
                  allowDecimal={false}
                  clampBehavior='strict'
                  disabled={isViewMode}
                  required
                  {...field}
                  onChange={(value) => {
                    const val = Number(value);
                    field.onChange(isNaN(val) ? undefined : val);
                  }}
                  onBlur={() => {
                    const val = Number(field.value);
                    if (isNaN(val) || val < WEB_MONITOR_WEB_TIMEOUT_MIN_VALUE) {
                      field.onChange(WEB_MONITOR_WEB_TIMEOUT_MIN_VALUE);
                    }
                    if (val > WEB_MONITOR_WEB_TIMEOUT_MAX_VALUE) {
                      field.onChange(WEB_MONITOR_WEB_TIMEOUT_MAX_VALUE);
                    }
                  }}
                  error={error?.message}
                />
              )}
            />
          </Grid.Col>

          <Grid.Col span={6}>
            <Controller
              name='browser'
              control={control}
              render={({ field, fieldState: { error } }) => {
                if (!enableFirefox && field.value === BrowserEnum.FIREFOX) {
                  field.onChange(BrowserEnum.CHROME);
                }

                return (
                  <Radio.Group label='Browser' required error={error?.message} value={field.value} onChange={isViewMode ? () => {} : field.onChange}>
                    <Flex align='center' h={34}>
                      <Group>
                        <Radio value={BrowserEnum.CHROME} label='Chrome' />
                        {enableFirefox && <Radio value={BrowserEnum.FIREFOX} label='Firefox' />}
                      </Group>
                    </Flex>
                  </Radio.Group>
                );
              }}
            />
          </Grid.Col>
        </Grid>
      </form>
    </Flex>
  );
};

export default BaseInformationSession;
