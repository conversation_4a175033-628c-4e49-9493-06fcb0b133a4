import React from 'react';
import { UseFormReturn } from 'react-hook-form';
import classes from './ApiConfigSession.module.css';
import { ExecutionApiInfoModel } from '@models/ExecutionApiInfoModel';
import { ExecutionParam } from '@core/schema/ExecutionParam';
import Section from './Section';
import ApiConfig from '@components/executionApiForm/ApiConfig';

interface Props {
  form: UseFormReturn<{ apiInfo: ExecutionApiInfoModel }>;
  isViewMode: boolean;
  executionParams?: Array<ExecutionParam>;
}

const ApiConfigSession: React.FC<Props> = ({ executionParams, form, isViewMode }) => {
  return (
    <Section label='API config'>
      <div className={classes.apiConfigContainer}>
        <ApiConfig
          form={form}
          isViewMode={isViewMode}
          variableNames={executionParams?.map((p) => p.name) || []}
          className={classes.apiConfigContainer}
        />
      </div>
    </Section>
  );
};

export default ApiConfigSession;
