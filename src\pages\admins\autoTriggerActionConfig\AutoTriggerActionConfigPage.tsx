import React, { useCallback, useEffect, useState } from 'react';
import { useNavigate, useParams, useSearchParams } from 'react-router-dom';
import { Box, Flex, Group, Stack, Title } from '@mantine/core';
import { KanbanButton, KanbanInput, KanbanNumberInput, KanbanSelect } from 'kanban-design-system';
import classes from './GroupConfigStyle.module.css';
import useFetch from '@core/hooks/useFetch';
import useMutate from '@core/hooks/useMutate';
import { Controller, useForm, UseFormReturn, useWatch } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import {
  AutoTriggerActionConfigTypeEnum,
  AutoTriggerConfigAction,
  DEFAULT_FORM_VALUE,
  DEFAULT_TIME_LAST_TRIGGER_VALUE,
  TRIGGER_TYPE_LABEL,
} from './Constants';
import {
  DESCRIPTION_MAX_LENGTH,
  MAX_ENABLE_TIME_SINCE_LAST_TRIGGER,
  MAX_NAME_LENGTH,
  MAX_TIME_SINCE_LAST_TRIGGER,
  MIN_TIME_SINCE_LAST_TRIGGER,
} from '@common/constants/ValidationConstant';
import { AclPermission } from '@models/AclPermission';
import GuardComponent from '@components/GuardComponent';
import { AutoTriggerActionConfigApi } from '@api/AutoTriggerActionConfigApi';
import { AutoTriggerActionConfigModel, AutoTriggerActionConfigModelSchema } from '@models/AutoTriggerActionConfigModel';
import ReferenceSession from './ReferenceSession';
import ConditionSession from './ConditionSession';
import ExecutionAction from './Action';
import { validateQuery } from '@components/queryBuilder';
import { RuleGroupType } from 'react-querybuilder';
import TimeType from './TimeType';

const SaveButton = ({ form }: { form: UseFormReturn<AutoTriggerActionConfigModel> }) => {
  const ruleGroup = useWatch({ control: form.control, name: 'ruleGroup' });
  const triggerType = useWatch({ control: form.control, name: 'triggerType' });
  const isRuleValid = triggerType === AutoTriggerActionConfigTypeEnum.CONDITION ? ruleGroup && validateQuery(ruleGroup as RuleGroupType) : true;

  const navigate = useNavigate();
  const { mutate: saveAutoTriggerActionConfigMutate } = useMutate(AutoTriggerActionConfigApi.save, {
    onSuccess: () => {
      navigate('../');
    },
  });

  const onSave = useCallback(() => {
    const parsedData = AutoTriggerActionConfigModelSchema.safeParse(form.getValues());

    if (parsedData.success) {
      const { applications, executions, services, ...rest } = parsedData.data;

      saveAutoTriggerActionConfigMutate({
        ...rest,
        serviceIds: services?.map((ele) => ele.id) || [],
        applicationIds: applications?.map((ele) => ele.id) || [],
        executionIds: (executions?.map((ele) => ele.id).filter(Boolean) as string[]) || [],
      });
    }
  }, [form, saveAutoTriggerActionConfigMutate]);

  return (
    <GuardComponent requirePermissions={[AclPermission.autoTriggerActionConfigEdit, AclPermission.autoTriggerActionConfigCreate]}>
      <KanbanButton onClick={onSave} disabled={!form.formState.isValid || !isRuleValid}>
        Save
      </KanbanButton>
    </GuardComponent>
  );
};

const AutoTriggerActionConfigFormPage = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { id } = useParams<{ id?: string }>();
  const autoTriggerActionConfigId = id && id !== '0' ? id : '';
  const isCreateMode = !autoTriggerActionConfigId;
  const [currentTab, setCurrentTab] = useState<string>(AutoTriggerConfigAction.CREATE);
  const isViewMode = currentTab === AutoTriggerConfigAction.VIEW;

  const form = useForm<AutoTriggerActionConfigModel>({
    defaultValues: DEFAULT_FORM_VALUE,
    resolver: zodResolver(AutoTriggerActionConfigModelSchema),
    mode: 'onChange',
  });

  const [triggerTypeState, setTriggerTypeState] = useState<AutoTriggerActionConfigTypeEnum>(DEFAULT_FORM_VALUE.triggerType);

  useEffect(() => {
    const tab = searchParams.get('action');
    if (tab) {
      setCurrentTab(tab);
    }
  }, [searchParams]);

  const { data: autoTriggerActionConfigData } = useFetch(AutoTriggerActionConfigApi.findById(autoTriggerActionConfigId), {
    enabled: !isCreateMode,
  });

  useEffect(() => {
    if (!isCreateMode && autoTriggerActionConfigData?.data) {
      const { data } = autoTriggerActionConfigData;
      const triggerType = data.triggerType ?? AutoTriggerActionConfigTypeEnum.CONDITION;

      form.reset({
        ...data,
        services: data.services || [],
        applications: data.applications || [],
        executions: data.executions || [],
        timeSinceLastTrigger: data.timeSinceLastTrigger ?? DEFAULT_TIME_LAST_TRIGGER_VALUE,
        triggerType: triggerType,
      });
      setTriggerTypeState(triggerType);
    }
  }, [autoTriggerActionConfigData, form, isCreateMode]);

  const { control } = form;
  const typeOptions = Object.values(AutoTriggerActionConfigTypeEnum).map((ele) => ({ label: TRIGGER_TYPE_LABEL[ele], value: ele }));

  return (
    <Box>
      <Flex justify='space-between' mb='sm' className={classes.groupConfigHeader}>
        <Title order={3}>
          {isViewMode ? 'View auto trigger action config' : isCreateMode ? 'Create auto trigger action config' : 'Update auto trigger action config'}
        </Title>
        <Flex>
          <Group>
            <KanbanButton variant='outline' onClick={() => navigate('../')}>
              Cancel
            </KanbanButton>
            {!isViewMode && <SaveButton form={form} />}
          </Group>
        </Flex>
      </Flex>

      <Stack gap='md'>
        <Stack className={classes.session} gap='xs'>
          <Title order={5}>General Information</Title>
          <Controller
            control={control}
            name='name'
            render={({ field, fieldState }) => (
              <KanbanInput
                disabled={isViewMode}
                label='Config name'
                required
                {...field}
                maxLength={MAX_NAME_LENGTH}
                error={fieldState.error?.message}
              />
            )}
          />
          <Controller
            control={control}
            name='description'
            render={({ field, fieldState }) => (
              <KanbanInput
                disabled={isViewMode}
                label='Description'
                {...field}
                maxLength={DESCRIPTION_MAX_LENGTH}
                error={fieldState.error?.message}
              />
            )}
          />
          <Controller
            control={control}
            name='triggerType'
            render={({ field: { onChange, value } }) => (
              <KanbanSelect
                label='Trigger by'
                placeholder='Select action type'
                data={typeOptions}
                value={value}
                disabled={isViewMode}
                required
                allowDeselect={false}
                onChange={(val) => {
                  onChange(val as AutoTriggerActionConfigTypeEnum);
                  setTriggerTypeState(val as AutoTriggerActionConfigTypeEnum);
                }}
              />
            )}
          />
        </Stack>

        {triggerTypeState === AutoTriggerActionConfigTypeEnum.CONDITION && (
          <>
            <Stack className={classes.session} gap='xs'>
              <Title order={5}>Reference</Title>
              <ReferenceSession isViewMode={isViewMode} form={form} />
            </Stack>
            <Stack className={classes.session} gap='xs'>
              <Title order={5}>Condition</Title>
              <ConditionSession isViewMode={isViewMode} form={form} />
            </Stack>
            <Stack className={classes.session} gap='xs'>
              <Title order={5}>Trigger config</Title>
              <Controller
                control={control}
                name='timeSinceLastTrigger'
                render={({ field, fieldState }) => (
                  <KanbanNumberInput
                    disabled={isViewMode}
                    label='Time since last trigger'
                    max={MAX_ENABLE_TIME_SINCE_LAST_TRIGGER}
                    allowDecimal={false}
                    allowNegative={false}
                    required
                    error={fieldState.error?.message}
                    {...field}
                    clampBehavior='strict'
                    onChange={(value) => {
                      const val = Number(value);
                      field.onChange(isNaN(val) ? undefined : val);
                    }}
                    onBlur={() => {
                      const val = Number(field.value);
                      if (isNaN(val) || val < MIN_TIME_SINCE_LAST_TRIGGER) {
                        field.onChange(MIN_TIME_SINCE_LAST_TRIGGER);
                      }
                      if (val > MAX_TIME_SINCE_LAST_TRIGGER) {
                        field.onChange(MAX_TIME_SINCE_LAST_TRIGGER);
                      }
                    }}
                  />
                )}
              />
            </Stack>
          </>
        )}

        {triggerTypeState === AutoTriggerActionConfigTypeEnum.TIME && (
          <Stack className={classes.session} gap='xs'>
            <Title order={5}>Time type</Title>
            <TimeType isViewMode={isViewMode} form={form} />
          </Stack>
        )}

        <Stack className={classes.session} gap='xs'>
          <Title order={5}>Action</Title>
          <ExecutionAction isViewMode={isViewMode} form={form} />
        </Stack>
      </Stack>
    </Box>
  );
};

export default AutoTriggerActionConfigFormPage;
