import React, { useEffect, useMemo } from 'react';
import { Flex, ComboboxData, ActionIcon, Box, Stack, Title } from '@mantine/core';
import { Controller, useFieldArray, UseFormReturn, useWatch } from 'react-hook-form';
import { IconPlus, IconTrash } from '@tabler/icons-react';
import { AutoTriggerActionConfigModel } from '@models/AutoTriggerActionConfigModel';
import useFetch from '@core/hooks/useFetch';
import { ExecutionApi } from '@api/ExecutionApi';
import { ExecutionTypeEnum, ExecutionTypeLabel } from '@common/constants/ExecutionConstants';
import { KanbanSelect } from 'kanban-design-system';
import { ExecutionModel } from '@models/ExecutionModel';

interface Props {
  form: UseFormReturn<AutoTriggerActionConfigModel>;
  isViewMode: boolean;
}

const MAX_BOX = 3;
const DEFAULT_EXECUTION_ITEM = {
  id: '',
  type: ExecutionTypeEnum.PYTHON,
};

interface ActionBoxProps {
  index: number;
  control: any;
  setValue: any;
  isViewMode: boolean;
}

function ActionBox({ control, index, isViewMode, setValue }: ActionBoxProps) {
  const {
    id,
    name,
    type: selectedType,
  } = useWatch({
    control,
    name: `executions.${index}`,
  }) as ExecutionModel;

  const allExecutions = useWatch({
    control,
    name: 'executions',
  }) as { id: string; type: ExecutionTypeEnum }[];

  const selectedIdsExcludingThis = useMemo(() => {
    return allExecutions.map((exe, idx) => (idx !== index ? exe.id : undefined)).filter((id): id is string => !!id);
  }, [allExecutions, index]);

  const { data: fetchResult } = useFetch(ExecutionApi.findByType(selectedType), {
    showLoading: false,
    enabled: !!selectedType,
  });

  const executionsData = useMemo(() => fetchResult?.data || [], [fetchResult]);

  const executionOptions: ComboboxData = useMemo(() => {
    if (!fetchResult || !fetchResult.data) {
      return id ? [{ value: id, label: name }] : [];
    }
    return executionsData
      .filter((e) => !selectedIdsExcludingThis.includes(e.id))
      .map((e) => ({
        value: e.id,
        label: `${e.executionGroupName} - ${e.name}`,
      }));
  }, [executionsData, fetchResult, id, name, selectedIdsExcludingThis]);

  const typeOptions: ComboboxData = useMemo(() => {
    return Object.values(ExecutionTypeEnum)
      .filter((t) => t !== ExecutionTypeEnum.SQL)
      .map((t) => ({
        value: t,
        label: ExecutionTypeLabel[t] ?? t,
      }));
  }, []);

  return (
    <Box bd='1px solid var(--mantine-color-default-border)' p='md'>
      <Flex justify='space-between' mb='xs'>
        <Title order={6}>{`Action ${index + 1}`}</Title>
      </Flex>

      <Controller
        name={`executions.${index}.type`}
        control={control}
        render={({ field: { onChange, value } }) => (
          <KanbanSelect
            label='Action Type'
            placeholder='Select action type'
            data={typeOptions}
            value={value}
            onChange={(val) => {
              onChange(val as ExecutionTypeEnum);
              setValue(`executions.${index}.id`, '', { shouldValidate: true, shouldDirty: true });
            }}
            disabled={isViewMode}
            required
            allowDeselect={false}
          />
        )}
      />

      <Controller
        name={`executions.${index}.id`}
        control={control}
        render={({ field: { onChange, value } }) => (
          <KanbanSelect
            label='Action Name'
            placeholder='Select action name'
            data={executionOptions}
            value={value}
            onChange={(val) => onChange(val)}
            disabled={isViewMode}
            searchable
            required
          />
        )}
      />
    </Box>
  );
}

const ExecutionAction = ({ form, isViewMode }: Props) => {
  const { control, setValue } = form;

  const { fields, insert, remove } = useFieldArray({
    control,
    name: 'executions',
    shouldUnregister: false,
  });

  useEffect(() => {
    if (fields.length === 0 && !isViewMode) {
      insert(0, DEFAULT_EXECUTION_ITEM);
    }
  }, [fields.length, insert, isViewMode]);

  return (
    <Flex direction='column' gap='sm'>
      <Stack>
        {fields.map((field, index) => (
          <React.Fragment key={`${field.id}-${index}`}>
            <ActionBox index={index} control={control} setValue={setValue} isViewMode={isViewMode} />
            <Flex justify='flex-end' mb='sm' gap='xs'>
              {!isViewMode && fields.length < MAX_BOX && (
                <ActionIcon onClick={() => insert(index + 1, DEFAULT_EXECUTION_ITEM)}>
                  <IconPlus />
                </ActionIcon>
              )}
              {!isViewMode && fields.length > 1 && (
                <ActionIcon onClick={() => remove(index)}>
                  <IconTrash />
                </ActionIcon>
              )}
            </Flex>
          </React.Fragment>
        ))}
      </Stack>
    </Flex>
  );
};

export default ExecutionAction;
