import {
  DESCRIPTION_MAX_LENGTH,
  MAX_NAME_LENGTH,
  MAX_TIME_SINCE_LAST_TRIGGER,
  MIN_STRING_LENGTH,
  MIN_TIME_SINCE_LAST_TRIGGER,
} from '@common/constants/ValidationConstant';
import { z } from 'zod';
import { QueryRuleGroupTypeModelSchema } from './RuleGroupTypeModel';
import { ApplicationSchema, ServiceSchema } from '@core/schema';
import { TriggerExecutionModelSchema } from './TriggerExecutionModel';
import { AutoTriggerActionConfigTypeEnum } from '@pages/admins/autoTriggerActionConfig/Constants';
import { isEmpty } from 'lodash';

export const AutoTriggerActionConfigModelSchema = z
  .object({
    id: z.string().optional(),
    name: z.string().trim().min(MIN_STRING_LENGTH).max(MAX_NAME_LENGTH),
    description: z.string().max(DESCRIPTION_MAX_LENGTH).optional(),
    triggerType: z.nativeEnum(AutoTriggerActionConfigTypeEnum),
    cronExpression: z.string().optional(),
    ruleGroup: QueryRuleGroupTypeModelSchema.optional(),
    services: z.array(ServiceSchema),
    applications: z.array(ApplicationSchema),
    executions: z.array(TriggerExecutionModelSchema),
    timeSinceLastTrigger: z.number().min(MIN_TIME_SINCE_LAST_TRIGGER).max(MAX_TIME_SINCE_LAST_TRIGGER).optional(),
  })
  .superRefine((value, ctx) => {
    if (value.triggerType === AutoTriggerActionConfigTypeEnum.TIME && isEmpty(value.cronExpression)) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        path: ['cronExpression'],
        message: 'Cron Expression can not be empty',
      });
    } else if (value.triggerType === AutoTriggerActionConfigTypeEnum.CONDITION && isEmpty(value.ruleGroup)) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        path: ['ruleGroup'],
        message: 'Condition can not be empty',
      });
    }
  });

export type AutoTriggerActionConfigModel = z.infer<typeof AutoTriggerActionConfigModelSchema>;
