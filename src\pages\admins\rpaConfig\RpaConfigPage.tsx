import useFetch from '@core/hooks/useFetch';
import { Card, Flex, Switch } from '@mantine/core';
import { KanbanButton, KanbanNumberInput, KanbanSelect, KanbanTooltip } from 'kanban-design-system';
import React, { useCallback, useEffect } from 'react';
import HeaderTitleComponent from '@components/HeaderTitleComponent';
import useMutate from '@core/hooks/useMutate';
import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { RpaConfigApi } from '@api/RpaConfigApi';
import { RpaConfigModel, RpaConfigModelSchema } from '@models/RpaConfigModel';
import { AclPermission } from '@models/AclPermission';
import GuardComponent from '@components/GuardComponent';
import { Title } from '@mantine/core';
import {
  MAX_ENABLE_RPA_CONFIG_INTERVAL_SECOND,
  RPA_CONFIG_INTERVAL_SECOND_MAX_VALUE,
  RPA_CONFIG_INTERVAL_SECOND_MIN_VALUE,
} from '@common/constants/ValidationConstant';
import { numberofRetryOptions } from '@common/constants/WebMonitorConfigConstant';

const defaultValue: RpaConfigModel = {
  id: undefined,
  interval: 5,
  numberOfRetry: 0,
  active: false,
};

export const RpaConfigPage = () => {
  const { data: rpaConfig, refetch: rpaConfigRefetch } = useFetch(RpaConfigApi.findRpaConfig(), {
    placeholderData: (prev) => prev,
  });

  const { mutate: activeOrInactive } = useMutate(RpaConfigApi.activeOrInactive, {
    successNotification: 'Update status of config successfully.!',
    onSuccess: () => {
      rpaConfigRefetch();
    },
  });

  const {
    control,
    formState: { isValid },
    handleSubmit,
    reset,
  } = useForm<RpaConfigModel>({
    resolver: zodResolver(RpaConfigModelSchema),
    mode: 'onChange',
    defaultValues: defaultValue,
  });

  useEffect(() => {
    if (rpaConfig?.data) {
      reset({
        id: rpaConfig?.data?.id,
        interval: rpaConfig?.data.interval,
        numberOfRetry: rpaConfig?.data.numberOfRetry,
        active: rpaConfig?.data.active,
      });
    }
  }, [rpaConfig, reset]);

  const { mutate: saveMutate } = useMutate(RpaConfigApi.save, {
    successNotification: { message: 'Save config successfully!' },
    onSuccess: () => {
      rpaConfigRefetch();
    },
  });

  const onSubmit = useCallback(
    (data: RpaConfigModel) => {
      if (isValid) {
        const validatedData = RpaConfigModelSchema.parse(data);
        saveMutate(validatedData);
      }
    },
    [isValid, saveMutate],
  );

  return (
    <>
      <HeaderTitleComponent
        title='RPA Config'
        rightSection={
          <GuardComponent requirePermissions={[AclPermission.rpaConfig]}>
            <Flex direction='row' gap='xs' align='center'>
              <KanbanButton size='xs' onClick={handleSubmit(onSubmit)} disabled={!isValid}>
                Save
              </KanbanButton>
            </Flex>
          </GuardComponent>
        }
      />
      <Card shadow='sm' radius='md' m={5} withBorder>
        <form onSubmit={handleSubmit(onSubmit)}>
          <Flex justify='space-between' align='center' w='100%'>
            <Title order={4}>General Information</Title>
            <GuardComponent requirePermissions={[AclPermission.rpaConfig]}>
              <KanbanTooltip label={rpaConfig?.data?.active ? 'Active' : 'InActive'}>
                <Flex align='center' gap='sm'>
                  <Controller
                    name='active'
                    control={control}
                    render={({ field }) => (
                      <Switch
                        checked={field.value}
                        onChange={({ currentTarget }) => {
                          field.onChange(currentTarget.checked);
                          rpaConfig?.data?.id?.toString() && activeOrInactive(rpaConfig.data.id.toString());
                        }}
                        label='Enable RPA Monitor'
                      />
                    )}
                  />
                </Flex>
              </KanbanTooltip>
            </GuardComponent>
          </Flex>
          <Controller
            name='interval'
            control={control}
            render={({ field, fieldState: { error } }) => (
              <KanbanNumberInput
                label='Interval (Seconds)'
                max={MAX_ENABLE_RPA_CONFIG_INTERVAL_SECOND}
                clampBehavior='strict'
                allowDecimal={false}
                allowNegative={false}
                placeholder={rpaConfig?.data?.interval?.toString() ?? ''}
                required
                {...field}
                error={error?.message}
                onChange={(value) => {
                  const val = Number(value);
                  field.onChange(isNaN(val) ? undefined : val);
                }}
                onBlur={() => {
                  const val = Number(field.value);
                  if (isNaN(val) || val < RPA_CONFIG_INTERVAL_SECOND_MIN_VALUE) {
                    field.onChange(RPA_CONFIG_INTERVAL_SECOND_MIN_VALUE);
                  }
                  if (val > RPA_CONFIG_INTERVAL_SECOND_MAX_VALUE) {
                    field.onChange(RPA_CONFIG_INTERVAL_SECOND_MAX_VALUE);
                  }
                }}
              />
            )}
          />
          <Controller
            name='numberOfRetry'
            control={control}
            render={({ field, fieldState }) => (
              <KanbanSelect
                label='Number of retry'
                required
                data={numberofRetryOptions}
                defaultValue={String(field.value ?? '0')}
                value={String(field.value)}
                onChange={(val) => field.onChange(Number(val))}
                error={fieldState.error?.message}
                allowDeselect={false}
              />
            )}
          />
        </form>
      </Card>
    </>
  );
};

export default RpaConfigPage;
