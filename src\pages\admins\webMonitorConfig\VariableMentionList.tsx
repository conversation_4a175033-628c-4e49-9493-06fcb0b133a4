import React, { forwardRef, useEffect, useImperativeHandle, useState, useMemo } from 'react';
import { SuggestionKeyDownProps, SuggestionProps } from '@tiptap/suggestion';
import { VariableApi } from '@api/VariableApi';
import useFetch from '@core/hooks/useFetch';
import styles from './VariableMentionList.module.scss';
import { Box } from '@mantine/core';
import { KanbanButton } from 'kanban-design-system';

export interface VariableMentionListProps extends SuggestionProps {}

export interface VariableMentionListActions {
  onKeyDown: (props: SuggestionKeyDownProps) => boolean;
}

export const VariableMentionList = forwardRef<VariableMentionListActions, VariableMentionListProps>(({ command, query }, ref) => {
  // Fetch variables directly from API
  const { data: variableData } = useFetch(VariableApi.findAll(), { showLoading: false });

  // Filter variables based on query using useMemo
  const allItems = useMemo(() => {
    const variables = variableData?.data || [];
    const lowerQuery = query.toLowerCase();

    return variables
      .filter((variable) => variable.name.toLowerCase().includes(lowerQuery))
      .map((variable) => ({
        id: variable.name,
        label: variable.name,
      }));
  }, [variableData, query]);

  const [selectedIndex, setSelectedIndex] = useState(0);

  const selectItem = (index: number) => {
    const item = allItems[index];

    if (item) {
      command({ id: item.id, label: item.label });
    }
  };

  const upHandler = () => {
    setSelectedIndex((selectedIndex + allItems.length - 1) % allItems.length);
  };

  const downHandler = () => {
    setSelectedIndex((selectedIndex + 1) % allItems.length);
  };

  const enterHandler = () => {
    selectItem(selectedIndex);
  };

  useEffect(() => setSelectedIndex(0), [allItems]);

  useImperativeHandle(ref, () => ({
    onKeyDown: ({ event }) => {
      if (event.key === 'ArrowUp') {
        upHandler();
        return true;
      }

      if (event.key === 'ArrowDown') {
        downHandler();
        return true;
      }

      if (event.key === 'Enter') {
        enterHandler();
        return true;
      }

      return false;
    },
  }));

  if (allItems.length === 0) {
    return (
      <Box className={styles.mentionList}>
        <Box className={styles.noResults}>No variables found</Box>
      </Box>
    );
  }

  return (
    <Box className={styles.mentionList}>
      {allItems.map((item, index) => (
        <KanbanButton
          className={`${styles.mentionItem} ${index === selectedIndex ? styles.selected : ''}`}
          key={index}
          onClick={() => selectItem(index)}
          type='button'>
          <Box className={styles.variableIcon}>@@</Box>
          <Box className={styles.variableInfo}>{item.label}</Box>
        </KanbanButton>
      ))}
    </Box>
  );
});

VariableMentionList.displayName = 'VariableMentionList';
