import React, { useState, useMemo, useRef, useCallback } from 'react';
import equal from 'fast-deep-equal';
import {
  ColumnType,
  KanbanButton,
  KanbanIconButton,
  KanbanTableProps,
  KanbanTableSelectHandleMethods,
  KanbanTooltip,
  TableAffactedSafeType,
} from 'kanban-design-system';
import { ActionIcon, Box, ComboboxData, Flex, Switch } from '@mantine/core';
import useFetch from '@core/hooks/useFetch';
import useMutate from '@core/hooks/useMutate';
import { IconEye, IconPlus, IconTrash, IconEdit } from '@tabler/icons-react';
import HeaderTitleComponent from '@components/HeaderTitleComponent';
import Table from '@components/table';
import { DEFAULT_DEBOUNCE_TIME } from '@components/ComboboxLoadMore';
import { SortType } from '@common/constants/SortType';
import { PaginationRequest } from '@api/Type';
import { DEFAULT_PAGINATION_REQUEST } from '@common/constants/PaginationRequestConstant';
import { AclPermission } from '@models/AclPermission';
import GuardComponent from '@components/GuardComponent';
import { createSearchParams, useNavigate } from 'react-router-dom';
import { BaseAutoTriggerActionConfig } from '@core/schema/AutoTriggerActionConfig';
import { AutoTriggerActionConfigApi } from '@api/AutoTriggerActionConfigApi';
import { AutoTriggerConfigAction, AutoTriggerConfigOperatorEnum, DEFAULT_CUSTOM_OBJECT_SEARCH_REQUEST } from './Constants';
import { ROUTE_PATH } from '@common/utils/RouterUtils';
import { getDefaultDeleteConfirmMessage } from '@common/utils/ConfirmMessageUtils';
import { PreviewCondition } from '@components/queryBuilder/PreviewCondition';
import { RuleGroupType } from 'react-querybuilder';
import { QueryBuilderField } from '@components/queryBuilder';
import { CustomObjectApi } from '@api/CustomObjectApi';
import { QueryBuilderOperatorEnum } from '@components/queryBuilder/QueryBuilderOperatorEnum';
import { Execution } from '@core/schema/Execution';
import { AlertPriorityConfigApi } from '@api/AlertPriorityConfigApi';

const AutoTriggerActionConfigPage = () => {
  const [tableAffected, setTableAffected] = useState<PaginationRequest>({
    ...DEFAULT_PAGINATION_REQUEST,
    size: 20,
  });
  const navigate = useNavigate();
  const tableRef = useRef<KanbanTableSelectHandleMethods>(null);

  const { data: listAutoTriggerConfigs, refetch: refetchList } = useFetch(AutoTriggerActionConfigApi.findAll(tableAffected), {
    placeholderData: (prev) => prev,
  });
  const { data: customObjectData } = useFetch(CustomObjectApi.findAll(DEFAULT_CUSTOM_OBJECT_SEARCH_REQUEST), { showLoading: false });
  const { data: priorityData } = useFetch(AlertPriorityConfigApi.findAll({ withDeleted: false }), { showLoading: false });
  const priorityOptions = useMemo<ComboboxData>(
    () => priorityData?.data?.map((priority) => ({ value: String(priority.id), label: priority.name })) || [],
    [priorityData?.data],
  );
  const fields = useMemo<QueryBuilderField[]>(() => {
    return [
      {
        name: 'content',
        label: 'Alert Content',
        placeholder: 'Please enter value',
        inputType: 'datetime-local',
        operators: [QueryBuilderOperatorEnum.CONTAINS, QueryBuilderOperatorEnum.DOES_NOT_CONTAIN],
      },
      {
        name: 'priority',
        label: 'Priority',
        placeholder: 'Please enter value',
        operators: [
          QueryBuilderOperatorEnum.IS,
          QueryBuilderOperatorEnum.IS_NOT,
          QueryBuilderOperatorEnum.IS_ONE_OF,
          QueryBuilderOperatorEnum.IS_NOT_ONE_OF,
        ],
        valueEditorType: 'multiselect',
        values: priorityOptions,
      },
      {
        name: 'recipient',
        label: 'Contact',
        placeholder: 'Please enter value',
        operators: [
          QueryBuilderOperatorEnum.IS,
          QueryBuilderOperatorEnum.IS_NOT,
          QueryBuilderOperatorEnum.IS_ONE_OF,
          QueryBuilderOperatorEnum.IS_NOT_ONE_OF,
          QueryBuilderOperatorEnum.CONTAINS,
          QueryBuilderOperatorEnum.DOES_NOT_CONTAIN,
        ],
      },
      ...(customObjectData?.data?.content?.map((customObject) => ({
        name: String(customObject.id),
        label: customObject.name,
        placeholder: 'Please enter value',
        operators: Object.values(QueryBuilderOperatorEnum) as QueryBuilderOperatorEnum[],
      })) || []),
    ];
  }, [customObjectData?.data?.content, priorityOptions]);

  const { mutate: activeOrInactive } = useMutate(AutoTriggerActionConfigApi.activeOrInactive, {
    successNotification: 'Update status of config successfully!',
    onSuccess: () => {
      tableRef.current?.deselectAll();
      refetchList();
    },
  });

  const { mutate: deleteByIdMutate } = useMutate(AutoTriggerActionConfigApi.deleteById, {
    successNotification: 'Deleted successfully!',
    onSuccess: () => {
      tableRef.current?.deselectAll();
      refetchList();
    },
    confirm: getDefaultDeleteConfirmMessage(),
  });

  const handleUpdateTablePagination = useCallback((data: TableAffactedSafeType<BaseAutoTriggerActionConfig>) => {
    setTableAffected((state) => ({
      ...state,
      page: data.page - 1,
      size: data.rowsPerPage,
      sortBy: data.sortedBy ? data.sortedBy : DEFAULT_PAGINATION_REQUEST.sortBy,
      sortOrder: data.isReverse ? SortType.ASC : SortType.DESC,
      search: data.search,
    }));
  }, []);

  const handleChangeURL = useCallback(
    (id: string | '', action: string | null) => {
      const basePath = ROUTE_PATH.AUTO_TRIGGER_ACTION_CONFIGS;
      const pathname = id ? `${basePath}/${id}` : basePath;
      navigate({
        pathname,
        search: createSearchParams({
          action: action || AutoTriggerConfigAction.CREATE,
        }).toString(),
      });
    },
    [navigate],
  );

  const columns = useMemo<ColumnType<BaseAutoTriggerActionConfig>[]>(
    () => [
      { title: 'Name', name: 'name', width: '15%' },
      { title: 'Description', name: 'description', width: '15%' },
      { title: 'Type', name: 'triggerType', width: '15%' },
      { title: 'Cron', name: 'cronExpression', width: '15%', sortable: false },
      {
        title: 'Condition',
        name: 'ruleGroup',
        width: '35%',
        sortable: false,
        customRender: (value) => (
          <PreviewCondition fields={fields} operators={Object.values(AutoTriggerConfigOperatorEnum)} value={value as RuleGroupType} />
        ),
      },
      {
        title: 'Executions',
        name: 'executions',
        width: '25%',
        sortable: false,
        customRender: (records: Execution[]) => {
          return (
            <Flex direction='column' gap='xs'>
              {records.map((execution, index) => (
                <Flex key={execution.id || index} direction='row' gap='xs' align='center'>
                  <span className='kanbanText'>
                    <strong>{execution.type}</strong> : {execution.name}
                  </span>
                </Flex>
              ))}
            </Flex>
          );
        },
      },
    ],
    [fields],
  );

  const tableProps: KanbanTableProps<BaseAutoTriggerActionConfig> = useMemo(
    () => ({
      columns: columns,
      data: listAutoTriggerConfigs?.data?.content || [],
      showNumericalOrderColumn: true,
      pagination: {
        enable: true,
        rowsPerPage: [20, 30, 50],
      },
      sortable: { enable: true },
      searchable: { enable: true, debounceTime: DEFAULT_DEBOUNCE_TIME },
      serverside: {
        totalRows: listAutoTriggerConfigs?.data?.totalElements ?? 0,
        onTableAffected: (dataSet) => {
          if (!equal(tableAffected, dataSet)) {
            handleUpdateTablePagination(dataSet);
          }
        },
      },
      showTopBar: true,
      actions: {
        customAction: (data) => (
          <>
            <GuardComponent requirePermissions={[AclPermission.autoTriggerActionConfigEdit]}>
              <KanbanTooltip label={data.active ? 'Active' : 'Inactive'}>
                <Switch checked={data.active || false} onClick={() => activeOrInactive(data.id)} />
              </KanbanTooltip>
            </GuardComponent>
            <GuardComponent requirePermissions={[AclPermission.autoTriggerActionConfigView]}>
              <KanbanTooltip label='View'>
                <KanbanIconButton variant='transparent' size='sm' onClick={() => handleChangeURL(data.id, AutoTriggerConfigAction.VIEW)}>
                  <IconEye />
                </KanbanIconButton>
              </KanbanTooltip>
            </GuardComponent>
            <GuardComponent requirePermissions={[AclPermission.autoTriggerActionConfigEdit]}>
              <KanbanTooltip label='Edit'>
                <KanbanIconButton variant='transparent' size='sm' onClick={() => handleChangeURL(data.id, AutoTriggerConfigAction.UPDATE)}>
                  <IconEdit />
                </KanbanIconButton>
              </KanbanTooltip>
            </GuardComponent>
            <GuardComponent requirePermissions={[AclPermission.autoTriggerActionConfigDelete]}>
              <ActionIcon
                variant='transparent'
                color='red'
                onClick={() =>
                  deleteByIdMutate(data.id, {
                    confirm: getDefaultDeleteConfirmMessage(data.name),
                  })
                }>
                <IconTrash width={20} height={24} />
              </ActionIcon>
            </GuardComponent>
          </>
        ),
      },
    }),
    [
      activeOrInactive,
      columns,
      deleteByIdMutate,
      handleChangeURL,
      handleUpdateTablePagination,
      listAutoTriggerConfigs?.data?.content,
      listAutoTriggerConfigs?.data?.totalElements,
      tableAffected,
    ],
  );

  return (
    <Box flex={1} p='sm' bg='white'>
      <HeaderTitleComponent
        title='Auto Trigger Action Config'
        rightSection={
          <GuardComponent requirePermissions={[AclPermission.autoTriggerActionConfigCreate]}>
            <Flex direction='row' gap='xs' align='center'>
              <KanbanButton size='xs' onClick={() => handleChangeURL('0', AutoTriggerConfigAction.CREATE)} leftSection={<IconPlus />}>
                Create New
              </KanbanButton>
            </Flex>
          </GuardComponent>
        }
      />
      <Table ref={tableRef} {...tableProps} />
    </Box>
  );
};

export default AutoTriggerActionConfigPage;
