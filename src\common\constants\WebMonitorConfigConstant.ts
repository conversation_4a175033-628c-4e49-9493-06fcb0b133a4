import { ComboboxItem } from '@mantine/core';
import { DEFAULT_LOCALE_EN } from 'cron-job/LocaleSelect';

/** ---------- Common utilities ---------- */
const createOptionsFromArray = (labels: string[], offset = 1): ComboboxItem[] =>
  labels.map((label, index) => ({
    value: (index + offset).toString(),
    label,
  }));

const createOptionsFromRange = (length: number, offset = 0): ComboboxItem[] =>
  Array.from({ length }, (_, i) => ({
    value: `${i + offset}`,
    label: `${i + offset}`,
  }));

export enum WebMonitorConfigAction {
  CREATE = 'CREATE',
  UPDATE = 'UPDATE',
  DELETE = 'DELETE',
  VIEW = 'VIEW',
}

export enum MonitorTypeEnum {
  DISCOVER = 'DISCOVER',
  LOGIN = 'LOGIN',
}

export const MONITOR_TYPE_LABEL: { [key in MonitorTypeEnum]: string } = {
  [MonitorTypeEnum.DISCOVER]: 'Discover',
  [MonitorTypeEnum.LOGIN]: 'Login',
};

export enum BrowserEnum {
  CHROME = 'CHROME',
  FIREFOX = 'FIREFOX',
}

export const BROWSER_LABEL: { [key in BrowserEnum]: string } = {
  [BrowserEnum.CHROME]: 'Chrome',
  [BrowserEnum.FIREFOX]: 'Firefox',
};

export enum FindElementByEnum {
  XPATH = 'XPATH',
  CSS_SELECTOR = 'CSS_SELECTOR',
  ID = 'ID',
  NAME = 'NAME',
}

export const FIND_ELEMENT_BY_LABEL: { [key in FindElementByEnum]: string } = {
  [FindElementByEnum.XPATH]: 'XPATH',
  [FindElementByEnum.CSS_SELECTOR]: 'CSS SELECTOR',
  [FindElementByEnum.ID]: 'ID',
  [FindElementByEnum.NAME]: 'NAME',
};

export enum ActionTypeEnum {
  CLICK = 'CLICK',
  DOUBLE_CLICK = 'DOUBLE_CLICK',
  SEND_KEY = 'SEND_KEY',
  CLEAR_INPUT = 'CLEAR_INPUT',
  SWITCH_FRAME = 'SWITCH_FRAME',
  SWITCH_TO_DEFAULT_FRAME = 'SWITCH_TO_DEFAULT_FRAME',
  HOVER = 'HOVER',
  SELECT_FROM_DROPDOWN = 'SELECT_FROM_DROPDOWN',
  WAIT = 'WAIT',
  WAITING_FOR_ELEMENT = 'WAITING_FOR_ELEMENT',
  FIND_ELEMENT = 'FIND_ELEMENT',
  SWITCH_TO_POPUP = 'SWITCH_TO_POPUP',
  CLOSE_POPUP = 'CLOSE_POPUP',
  BACK_TO_MAIN = 'BACK_TO_MAIN',
  ALERT_ACCEPT = 'ALERT_ACCEPT',
  ALERT_DISMISS = 'ALERT_DISMISS',
  GO_TO_URL = 'GO_TO_URL',
}

export const ACTION_TYPE_LABEL: { [key in ActionTypeEnum]: string } = {
  [ActionTypeEnum.CLICK]: 'Click',
  [ActionTypeEnum.DOUBLE_CLICK]: 'Double click',
  [ActionTypeEnum.SEND_KEY]: 'Send key',
  [ActionTypeEnum.CLEAR_INPUT]: 'Clear input',
  [ActionTypeEnum.SWITCH_FRAME]: 'Switch to frame',
  [ActionTypeEnum.SWITCH_TO_DEFAULT_FRAME]: 'Switch to default frame',
  [ActionTypeEnum.HOVER]: 'Hover',
  [ActionTypeEnum.SELECT_FROM_DROPDOWN]: 'Select from dropdown',
  [ActionTypeEnum.WAIT]: 'Wait',
  [ActionTypeEnum.WAITING_FOR_ELEMENT]: 'Waiting for element',
  [ActionTypeEnum.FIND_ELEMENT]: 'Find element',
  [ActionTypeEnum.SWITCH_TO_POPUP]: 'Switch to popup',
  [ActionTypeEnum.CLOSE_POPUP]: 'Close popup',
  [ActionTypeEnum.BACK_TO_MAIN]: 'Back to main page',
  [ActionTypeEnum.ALERT_ACCEPT]: 'Accept alert',
  [ActionTypeEnum.ALERT_DISMISS]: 'Dismiss alert',
  [ActionTypeEnum.GO_TO_URL]: 'Go to url',
};

/** ---------- Reusable options ---------- */
export const monthOptions: ComboboxItem[] = createOptionsFromArray(DEFAULT_LOCALE_EN.months);
export const weekOptions: ComboboxItem[] = createOptionsFromArray(DEFAULT_LOCALE_EN.weekDays);
export const dayOptions: ComboboxItem[] = createOptionsFromRange(31, 1); // 1-31
export const hourOptions: ComboboxItem[] = createOptionsFromRange(24); // 0-23
export const minuteOptions: ComboboxItem[] = createOptionsFromRange(60); // 0-59
export const numberofRetryOptions: ComboboxItem[] = createOptionsFromRange(4); // 0-3
